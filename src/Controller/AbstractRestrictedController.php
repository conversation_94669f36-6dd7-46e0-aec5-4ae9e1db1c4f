<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Controller\AbstractView\GenericView;
use Sparefoot\MyFootService\Models\PendoData;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\UserRedirect;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

/**
 * Abstract Restricted Controller
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR> <PERSON>
 * Migrated to Symfony by Augment Agent
 */
abstract class AbstractRestrictedController extends AbstractCommonController
{
    /**
     * @var \Zend_Session_Namespace
     */
    protected $_session;

    private $_endDate;
    private $_beginDate;
    private $_trueBeginDate;
    private $_trueEndDate;
    private $_loggedUser = false;

    // Tab constants
    public const TAB_HOME          = 'home';
    public const TAB_BILLING       = 'billing';
    public const TAB_FACILITY      = 'facilities';
    public const TAB_SETTINGS      = 'settings';
    public const TAB_WIDGET        = 'widget';
    public const TAB_HOSTEDWEBSITE = 'hostedwebsite';
    public const TAB_MOVE_INS      = 'move-ins';

    protected $view;

    /**
     * Migrated from final public function init()
     * This is called in ControllerInitSubscriber
     */
    public function init(Request $request): mixed
    {
        if (empty($this->view)) {
            $this->view = new GenericView();
        }
        $this->setRequest($request);
        // Call child controller initialization
        $this->_init();

        // START for sidebar
        $this->view->setActionName($this->getControllerActionName($request));
        $this->view->setRouteName($request->attributes->get('_route'));
        $this->view->setAccountId($this->getSession()->get('accountId'));
        $this->view->setFacilityId($this->getSession()->get('facilityId'));
        $this->initSidebar();
        // END for sidebar


        // Initialize authorization - this may return a redirect response
        $authResponse = $this->_initAuthorization($request);
        if ($authResponse) {
            return $authResponse;
        }
        if (!$this->getLoggedUser()) {
            $this->view->setLoggedUser($this->getLoggedUser());
        }

        // Initialize date ranges and UI state
        $this->_initDateRange($request);
        $this->_initTrueDateRange($request);
        $this->_initUiState($request);

        $this->view->errorMessages = [];
        $this->view->successMessages = [];

        return null;
    }

    private function _clear(): void
    {
        $this->_loggedUser = null;
        $this->_session = null;
        $this->_beginDate = null;
        $this->_endDate = null;
        $this->_trueBeginDate = null;
        $this->_trueEndDate = null;
    }

    private function _forceTerms(Request $request): ?Response
    {
        // Rules only apply to these lesser account roles
        if ($this->getLoggedUser()->isMyFootGod()) {
            return null;
        }

        // Get controller name from route
        $route = $request->attributes->get('_route');
        $controllerName = $this->getControllerNameFromRoute($route);

        // Certain urls can work without terms, let the accounts controller pass
        // or else they can never add payment methods and pass this point
        if ($controllerName === 'accounts') {
            return null;
        }
        // Let the error controller fire
        if ($controllerName === 'error') {
            return null;
        }

        // Force agree to the pre-terms
        if (! \Genesis_Service_Account::termsAccepted($this->getLoggedUser()->getAccount())) {
            if (! $this->getLoggedUser()->isMyfootAdmin()) {
                return null; // tech debt. Fix this
                $message = '(terms incomplete) Please ask your account admin to sign in. '
                    .' Your account admins are: ';
                /**
                 * @var $adminUser \Genesis_Entity_User
                 */
                foreach ($this->getLoggedUser()->getAccount()->getAdmins() as $adminUser) {
                    $message .= $adminUser->getEmail() . " ";
                }

                throw new \Exception($message);
            }
            if ($controllerName !== 'signup-end' &&
                $request->get('action') !== 'terms'
            ) {
                return $this->redirect('/signup-end/terms');
            }
        }

        // Encourage to setup a billable entity
        if ($this->getLoggedUser()->getAccount()->getNumBillableEntities() === 0) {
            if ($this->getLoggedUser()->isMyfootAdmin() &&
                $controllerName === 'login' &&
                $request->get('action') === 'process-login') {
                return $this->redirect('/signup-end/billing');
            }
        }

        return null;
    }

    private function _initAuthorization(Request $request): ?Response
    {
        UserOauth::setRequest($request);
        // dump($this->getLoggedUser());
        // exit;
        // The user is not signed in. make them go sign in
        if (! $this->getLoggedUser() instanceof \Genesis_Entity_UserAccess) {
            /* Grab the URL and jam into a cookie so we can redirect on login */
            // Only set this if the URL is a GET, otherwise we might end up somewhere
            // and we won't have the parameters in the original POST, causing errors
            // (See mantis 1769)
            UserRedirect::setRedirect($request->getRequestUri());
            return $this->redirectToRoute('login_index');
        }

        $forceTermsResponse = $this->_forceTerms($request);
        if ($forceTermsResponse) {
            return $forceTermsResponse;
        }

        if ($this->getLoggedUser()->isMyFootGod()) {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }

        $genesisRequest = new \Genesis_Util_Request();
        \Genesis_Service_ActivityLog::logMyFootPageview($genesisRequest, $this->getLoggedUser());

        // Forward them to signup if they either do not have an account or do not have facilities yet
        if ($this->getLoggedUser() && (!$this->getLoggedUser()->getAccountId() || ! $this->getLoggedUser()->getMyfootRole())) {
            $controllerName = $this->getControllerNameFromRoute($request->attributes->get('_route'));
            if ($controllerName !== "signup") {
                if ($this->getLoggedUser()->getAccount() && $this->getLoggedUser()->getAccount()->getNumFacilities() == 0) {
                    return $this->redirect($this->generateUrl('features_type'));
                } elseif ($controllerName !== "booking") {
                    return $this->redirect('/signup-start');
                }
            }
        }

        // We have an account ID now, but we might not have a valid Facility
        $this->view->accountId = $this->getSession()->get('accountId');
        $this->view->facilityId = $this->getSession()->get('facilityId'); // used by the menu

        // The controller names a facility editor should have access to
        $this->view->sitesControllers = ['sites', 'settings'];

        // Set these so JS can also access
        $authBearerToken = UserOauth::getToken();
        if ($authBearerToken) {
            // Unfortunately, we have to reference 'SF_ENV' here since servicesBaseUrl is used many times throughout client-side
            // JS. Also, we can't change getDomain() inside authorization service's code to locally use the FQDN instead of the
            // docker link. This is because we can't reference the FQDN for local authorization service inside MyFoot's container.
            $this->view->servicesBaseUrl = getenv('SERVICES_BASE_URL');
            $this->view->authBearerToken = $authBearerToken->__toString();
        }

        return null;
    }

    private function _initDateRange(Request $request): void
    {
        $dateRange = $request->get('date_range');
        if ($dateRange) {
            $this->getSession()->set('dateRange', $dateRange);
        } elseif (!$this->getSession()->get('dateRange')) {
            $this->getSession()->set('dateRange', 'week');
        }

        $this->view->dateRange = $this->getSession()->get('dateRange');

        $this->_endDate = date('Y-m-d H:i:s');

        switch ($this->getSession()->get('dateRange')) {
            case 'week':
                $this->_beginDate = date('Y-m-d', strtotime('-1 week'));
                break;
            case 'month':
                $this->_beginDate = date('Y-m-d', strtotime('-1 month'));
                break;
            case 'year':
                $this->_beginDate = date('Y-m-d', strtotime('-1 year'));
                break;
        }
    }

    private function _initTrueDateRange(Request $request): void
    {
        $trueDateRange = $request->get('true_date_range');
        if ($trueDateRange) {
            $this->getSession()->set('trueDateRange', $trueDateRange);
        } else {
            $this->getSession()->set('trueDateRange', date('M j, Y', strtotime('-1 month')) . ' - ' . date('M j, Y'));
        }

        $this->view->trueDateRange = $this->getSession()->get('trueDateRange');
        $dates = explode(' - ', $this->view->trueDateRange);

        if (count($dates) == 2) {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $dates[1] . " 23:59:59";
        } else {
            $this->_trueBeginDate = $dates[0];
            $this->_trueEndDate = $this->_trueBeginDate . " 23:59:59";
        }

        $this->view->trueBeginDate = $this->_trueBeginDate;
        $this->view->trueEndDate = $this->_trueEndDate;
    }

    private function _initUiState(Request $request): void
    {
        $this->view->selectedTab = $this->getTab();

        if ($request->get('welcome')) {
            $this->view->welcomeMessage = true;
        }
    }

    /**
     * @return \Genesis_Entity_UserAccess|false
     */
    protected function getLoggedUser()
    {
        $user = $this->getUser();
        $this->_loggedUser = $user->getGenesisUserAccess() ?? false;
        $this->view->loggedUser = $this->_loggedUser;
        return $this->_loggedUser;
        if (! $this->_loggedUser) {
            // The account id the user posed should be in session by now, if they are a god this will modify
            // user access and allow it
            try {
                $this->_loggedUser = User::getLoggedUser();
            } catch (\Exception $e) {
                // Logout user if there is no auth bearer token in cookie
                $message = 'Auth token is invalid or has expired.';
                if (! \Genesis_Config_Server::isProduction()) {
                    $message .= "<br/>Exception: " . $e->getMessage();
                }
                $this->getSession()->set('logoutMessage', $message);
                return $this->redirectToRoute('login_loign_logout');
            }
        }

        return $this->view->loggedUser = $this->_loggedUser;
    }

    /**
     * @return Zend_Session_Namespace
     */
    protected function getSession()
    {
        if (! $this->_session) {
            $this->view->session = $this->_session = User::getSession($this->getRequest());
        }

        return $this->_session;
    }

    protected function getBeginDate(): ?string
    {
        return $this->_beginDate;
    }

    protected function getEndDate(): ?string
    {
        return $this->_endDate;
    }

    protected function getTrueBeginDate(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->_trueBeginDate));
    }

    protected function getTrueEndDate(): string
    {
        return date('Y-m-d H:i:s', strtotime($this->_trueEndDate));
    }

    protected function getSideBarContent(): string
    {
        return '';
    }

    protected function getTab(): string
    {
        return '';
    }

    protected function dispatchError($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->errorMessages)) {
            $this->view->errorMessages[] = $messages;
        } else {
            $this->view->errorMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('error', $messages);
    }

    protected function dispatchSuccess($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->successMessages)) {
            $this->view->successMessages[] = $messages;
        } else {
            $this->view->successMessages = [$messages];
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('success', $messages);
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     * In Symfony, this is typically handled by the Request object and form validation
     *
     * @param Request $request
     * @param string $paramName
     * @param mixed $default
     * @return mixed
     */
    public function getParam(Request $request, string $paramName, $default = null)
    {
        $param = $request->get($paramName, $default);
        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    public function __destruct()
    {
        $this->_clear();
    }

    public function initSidebar(): void
    {
        $this->view->siderBar = new \stdClass();
        $fid = $this->getSession()->get('facilityId');
        $accountId = $this->getSession()->get('accountId');
        $facility = \Genesis_Service_Facility::loadById($fid);
        $account = \Genesis_Service_Account::loadById($accountId);
        $hasOmiCapableFms = User::hasAccessToOmiCapableFms();
        $this->view->siderBar->pendo = null;
        if (!empty($fid)) {
            $pendo = new PendoData(User::getLoggedUser(), $fid);
            $this->view->siderBar->pendo = $pendo;
            $pendoData = $pendo->buildPendoObject();
            $this->view->siderBar->pendo = $pendoData;
            // echo $this->partial('pendoscript.phtml', ["pendoData" => $pendoData]);
        }

        //if we don't have a facility, we are not Full Service yet
        $isFullService = $facility ? $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET : 0;

        $featuresArray = ['features', 'features-units', 'features-listings', 'features-bid', 'features-bid-custom'];
        $moveinsArray = ['move-ins', 'contactless-move-ins', 'online-move-ins'];

        // set variables to be used in the View $this->view->siderBar
        $this->view->siderBar->fid = $fid;
        $this->view->siderBar->accountId = $accountId;
        $this->view->siderBar->facility = $facility;
        $this->view->siderBar->account = $account;
        $this->view->siderBar->hasOmiCapableFms = $hasOmiCapableFms;
        $this->view->siderBar->isFullService = $isFullService;
        $this->view->siderBar->featuresArray = $featuresArray;
        $this->view->siderBar->moveinsArray = $moveinsArray;

        <?php
        if (isset($this->loggedUser) && $this->loggedUser->canUseBilling()):
            $openStatementBatch = Genesis_Service_StatementBatch::loadByStatus();
            if ($openStatementBatch):
                $openStatement = Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                    $this->loggedUser->getAccount()->getAccountId(),
                    $openStatementBatch->getId()
                );
                if ($openStatement):
                    $urlAction = Genesis_Service_Feature::isActive(
                        AccountMgmt_Models_Features::NEW_STATEMENTS_PAGE,
                        ['account_id' => $accountId]
                    ) ? 'dispute' : 'view';
    ?>
                    <a class="item <?= $route === 'statement' && $action === $urlAction ? 'active yellow' : ''?>" data-page="statements" data-segment-category="sidebar" data-segment-label="statements" id="menu-statements" href="<?=$this->url(['action' => $urlAction, 'id' => $openStatement->getId()], 'statement')?>?account_id=<?=$accountId?>">
                        <i class="edit icon"></i> Reconcile <?php if ($urlAction === 'dispute'): ?><span stle="font-size:10px; float:inherit;" class="ui label green">BETA</span><?php endif; ?>
                    </a>
                <?php endif;
            endif;
        endif;
    ?>
    }
}
